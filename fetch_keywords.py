from google.ads.googleads.client import GoogleAdsClient
from google.ads.googleads.errors import GoogleAdsException
from google.ads.googleads.v14.services.types import KeywordPlanIdeaService
from google.ads.googleads.v14.enums.types import KeywordPlanNetworkEnum

def main():
    # حمّل بيانات الاتصال من ملف google-ads.yaml
    client = GoogleAdsClient.load_from_storage("google-ads.yaml")

    # اختار الـ customer ID بتاعك (ممكن يكون 10 أرقام، لو مش معاك هديك طريقة تجيبه)
    customer_id = "138-688-4705"

    # الكلمة اللي هتبدأ بيها البحث
    seed_keyword = "ai startup"

    # إعدادات البحث
    keyword_plan_idea_service = client.get_service("KeywordPlanIdeaService")

    # Network ممكن يكون GOOGLE_SEARCH أو GOOGLE_SEARCH_AND_PARTNERS
    keyword_seed = client.get_type("KeywordSeed")
    keyword_seed.keywords.append(seed_keyword)

    request = client.get_type("GenerateKeywordIdeasRequest")
    request.customer_id = customer_id
    request.keyword_seed.keywords.append(seed_keyword)
    request.geo_target_constants.append("geoTargetConstants/1002711")  # مصر
    request.language = "languageConstants/1003"  # اللغة الإنجليزية
    request.keyword_plan_network = KeywordPlanNetworkEnum.KeywordPlanNetwork.GOOGLE_SEARCH

    # ابعت الطلب
    response = keyword_plan_idea_service.generate_keyword_ideas(request=request)

    print(f"\n🔍 أفضل كلمات مفتاحية مرتبطة بـ: {seed_keyword}\n")
    print(f"{'الكلمة':<35} | {'Search Volume':<15} | {'CPC (EGP)':<12} | {'المنافسة'}")
    print("-" * 80)

    for idea in response:
        keyword = idea.text
        volume = idea.keyword_idea_metrics.avg_monthly_searches
        cpc_micros = idea.keyword_idea_metrics.high_top_of_page_bid_micros
        cpc_egp = cpc_micros / 1_000_000  # نحول من micros لـ EGP
        competition = idea.keyword_idea_metrics.competition.name

        print(f"{keyword:<35} | {volume:<15} | {cpc_egp:<12.2f} | {competition}")

if __name__ == "__main__":
    main()
