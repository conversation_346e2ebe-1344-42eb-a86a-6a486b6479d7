from google.ads.googleads.client import GoogleAdsClient

print("Google Ads SDK is working ✅")


from google.ads.googleads.client import GoogleAdsClient
from google.ads.googleads.errors import GoogleAdsException
# مش محتاج تستورد KeywordPlanIdeaService و KeywordPlanNetworkEnum مباشرة
# from google.ads.googleads.v14.services.types import KeywordPlanIdeaService
# from google.ads.googleads.v14.enums.types import KeywordPlanNetworkEnum

def main():
    # حمّل بيانات الاتصال من ملف google-ads.yaml
    client = GoogleAdsClient.load_from_storage("google-ads.yaml")

    # اختار الـ customer ID بتاعك (ممكن يكون 10 أرقام، لو مش معاك هديك طريقة تجيبه)
    customer_id = "138-688-4705"

    # الكلمة اللي هتبدأ بيها البحث
    seed_keyword = "ai startup"

    # إعدادات البحث
    keyword_plan_idea_service = client.get_service("KeywordPlanIdeaService")

    request = client.get_type("GenerateKeywordIdeasRequest")
    request.customer_id = customer_id
    
    # الجزء ده هو اللي تم تعديله
    # بدل ما تعمل keyword_seed منفصل وتضيفه، ممكن تضيف الكلمة مباشرة كده:
    request.keyword_seed.keywords.append(seed_keyword) 
    
    request.geo_target_constants.append("geoTargetConstants/1002711")  # مصر
    request.language = "languageConstants/1003"  # اللغة الإنجليزية
    # تعديل طريقة استدعاء الـ enum
    request.keyword_plan_network = client.enums.KeywordPlanNetworkEnum.Google Search

    # ابعت الطلب
    try:
        response = keyword_plan_idea_service.generate_keyword_ideas(request=request)

        print(f"\n🔍 أفضل كلمات مفتاحية مرتبطة بـ: {seed_keyword}\n")
        print(f"{'الكلمة':<35} | {'Search Volume':<15} | {'CPC (EGP)':<12} | {'المنافسة'}")
        print("-" * 80)

        for idea in response:
            keyword = idea.text
            # بعض الكلمات ممكن ميكونش ليها بيانات، فبنعمل check
            volume = idea.keyword_idea_metrics.avg_monthly_searches if idea.keyword_idea_metrics.HasField("avg_monthly_searches") else "N/A"
            cpc_micros = idea.keyword_idea_metrics.high_top_of_page_bid_micros if idea.keyword_idea_metrics.HasField("high_top_of_page_bid_micros") else 0
            cpc_egp = cpc_micros / 1_000_000  # نحول من micros لـ EGP
            competition = idea.keyword_idea_metrics.competition.name if idea.keyword_idea_metrics.HasField("competition") else "N/A"

            print(f"{keyword:<35} | {volume:<15} | {cpc_egp:<12.2f} | {competition}")
    except GoogleAdsException as ex:
        print(
            f'Request with ID "{ex.request_id}" failed with status '
            f'"{ex.error.code().name}" and includes the following errors:'
        )
        for error in ex.errors:
            print(f'\tError with message "{error.message}".')
            if error.location:
                for field_path_element in error.location.field_path_elements:
                    print(f'\t\tOn field: {field_path_element.field_name}')

if __name__ == "__main__":
    main()

from google.ads.googleads.client import GoogleAdsClient
from google.ads.googleads.errors import GoogleAdsException
import os # عشان نتحقق من وجود ملف الكونفيج

def main():
    # حمّل بيانات الاتصال من ملف google-ads.yaml
    # تأكد إن ملف google-ads.yaml موجود في نفس مكان السكريبت أو المسار بتاعه صحيح
    config_path = "google-ads.yaml"
    if not os.path.exists(config_path):
        print(f"⚠️  ملف الكونفيج {config_path} مش موجود. لازم تحطه في نفس مكان السكريبت.")
        print("تقدر تلاقي نموذج لملف الكونفيج في توثيقات Google Ads API.")
        return

    try:
        client = GoogleAdsClient.load_from_storage(config_path)
    except Exception as e:
        print(f"❌  فيه مشكلة في تحميل ملف الكونفيج {config_path}: {e}")
        print("تأكد إن الفورمات بتاع الملف صح وإن كل البيانات موجودة.")
        return

    # اختار الـ customer ID بتاعك (ممكن يكون 10 أرقام، لو مش معاك هديك طريقة تجيبه)
    # ده بيكون الـ Account ID بتاع حسابك في Google Ads بدون فواصل
    customer_id = "138-688-4705" # ده مجرد مثال، لازم تغيره بالـ ID بتاعك

    # اطلب من المستخدم يدخل موضوع الـ essay
    seed_keyword = input("اكتب موضوع الـ essay الأساسي بتاعك (مثلاً: Climate Change Impact): ")
    if not seed_keyword:
        print("مفيش موضوع دخلته، مش هقدر أجيب كلمات مفتاحية.")
        return

    # إعدادات البحث والخدمة
    keyword_plan_idea_service = client.get_service("KeywordPlanIdeaService")

    request = client.get_type("GenerateKeywordIdeasRequest")
    request.customer_id = customer_id
    
    # إضافة الكلمة الأساسية اللي دخلها المستخدم
    request.keyword_seed.keywords.append(seed_keyword)
    
    # تحديد الموقع الجغرافي: هنا بنحدد مصر (geoTargetConstants/1002711)
    request.geo_target_constants.append("geoTargetConstants/1002711")
    
    # تحديد اللغة: الإنجليزية (languageConstants/1003)
    request.language = "languageConstants/1003"
    
    # تحديد شبكة البحث: Google Search فقط
    request.keyword_plan_network = client.enums.KeywordPlanNetworkEnum.Google Search

    print(f"\nجاري البحث عن كلمات مفتاحية مرتبطة بـ: '{seed_keyword}'...")

    # ابعت الطلب وحاول تتعامل مع أي أخطاء ممكن تحصل
    try:
        response = keyword_plan_idea_service.generate_keyword_ideas(request=request)

        print(f"\n🔍 أفضل كلمات مفتاحية ممكن تساعدك في الـ essay بتاعتك عن: {seed_keyword}\n")
        print(f"{'الكلمة المقترحة':<40} | {'متوسط البحث الشهري':<25} | {'المنافسة (للمعلنين)'}")
        print("-" * 100)

        found_ideas = False
        for idea in response:
            found_ideas = True
            keyword = idea.text
            
            # بنتحقق إذا كان الحقل موجود قبل ما نحاول نوصل للبيانات
            volume = idea.keyword_idea_metrics.avg_monthly_searches if idea.keyword_idea_metrics.HasField("avg_monthly_searches") else "غير متاح"
            competition = idea.keyword_idea_metrics.competition.name if idea.keyword_idea_metrics.HasField("competition") else "غير متاح"

            print(f"{keyword:<40} | {volume:<25} | {competition}")
        
        if not found_ideas:
            print("مفيش كلمات مفتاحية مقترحة حالياً للموضوع ده. ممكن تجرب موضوع تاني أو كلمة أساسية أعم.")

    except GoogleAdsException as ex:
        print(
            f'❌  الطلب فشل بمعرف "{ex.request_id}" وحالة الخطأ هي '
            f'"{ex.error.code().name}"، وبتتضمن الأخطاء التالية:'
        )
        for error in ex.errors:
            print(f'\tرسالة الخطأ: "{error.message}".')
            if error.location:
                for field_path_element in error.location.field_path_elements:
                    print(f'\t\tفي الحقل: {field_path_element.field_name}')
    except Exception as e:
        print(f"❌  حصل خطأ غير متوقع: {e}")

if __name__ == "__main__":
    main()


from google.ads.googleads.client import GoogleAdsClient
from google.ads.googleads.errors import GoogleAdsException
import os # عشان نتحقق من وجود ملف الكونفيج

def main():
    # حمّل بيانات الاتصال من ملف google-ads.yaml
    # تأكد إن ملف google-ads.yaml موجود في نفس مكان السكريبت أو المسار بتاعه صحيح
    config_path = "google-ads.yaml"
    if not os.path.exists(config_path):
        print(f"⚠️  ملف الكونفيج {config_path} مش موجود. لازم تحطه في نفس مكان السكريبت.")
        print("تقدر تلاقي نموذج لملف الكونفيج في توثيقات Google Ads API.")
        return

    try:
        client = GoogleAdsClient.load_from_storage(config_path)
    except Exception as e:
        print(f"❌  فيه مشكلة في تحميل ملف الكونفيج {config_path}: {e}")
        print("تأكد إن الفورمات بتاع الملف صح وإن كل البيانات موجودة.")
        return

    # اختار الـ customer ID بتاعك (ممكن يكون 10 أرقام، لو مش معاك هديك طريقة تجيبه)
    # ده بيكون الـ Account ID بتاع حسابك في Google Ads بدون فواصل
    customer_id = "138-688-4705" # ده مجرد مثال، لازم تغيره بالـ ID بتاعك

    # اطلب من المستخدم يدخل موضوع الـ essay
    seed_keyword = input("اكتب موضوع الـ essay الأساسي بتاعك (مثلاً: Climate Change Impact): ")
    if not seed_keyword:
        print("مفيش موضوع دخلته، مش هقدر أجيب كلمات مفتاحية.")
        return

    # إعدادات البحث والخدمة
    keyword_plan_idea_service = client.get_service("KeywordPlanIdeaService")

    request = client.get_type("GenerateKeywordIdeasRequest")
    request.customer_id = customer_id
    
    # إضافة الكلمة الأساسية اللي دخلها المستخدم
    request.keyword_seed.keywords.append(seed_keyword)
    
    # تحديد الموقع الجغرافي: هنا بنحدد مصر (geoTargetConstants/1002711)
    request.geo_target_constants.append("geoTargetConstants/1002711")
    
    # تحديد اللغة: الإنجليزية (languageConstants/1003)
    request.language = "languageConstants/1003"
    
    # تحديد شبكة البحث: Google Search فقط
    # تم تصحيح هذا السطر: استخدام Google Search بدلاً من 'Google Search'
    request.keyword_plan_network = client.enums.KeywordPlanNetworkEnum.Google Search 

    print(f"\nجاري البحث عن كلمات مفتاحية مرتبطة بـ: '{seed_keyword}'...")

    # ابعت الطلب وحاول تتعامل مع أي أخطاء ممكن تحصل
    try:
        response = keyword_plan_idea_service.generate_keyword_ideas(request=request)

        print(f"\n🔍 أفضل كلمات مفتاحية ممكن تساعدك في الـ essay بتاعتك عن: {seed_keyword}\n")
        print(f"{'الكلمة المقترحة':<40} | {'متوسط البحث الشهري':<25} | {'المنافسة (للمعلنين)'}")
        print("-" * 100)

        found_ideas = False
        for idea in response:
            found_ideas = True
            keyword = idea.text
            
            # بنتحقق إذا كان الحقل موجود قبل ما نحاول نوصل للبيانات
            volume = idea.keyword_idea_metrics.avg_monthly_searches if idea.keyword_idea_metrics.HasField("avg_monthly_searches") else "غير متاح"
            competition = idea.keyword_idea_metrics.competition.name if idea.keyword_idea_metrics.HasField("competition") else "غير متاح"

            print(f"{keyword:<40} | {volume:<25} | {competition}")
        
        if not found_ideas:
            print("مفيش كلمات مفتاحية مقترحة حالياً للموضوع ده. ممكن تجرب موضوع تاني أو كلمة أساسية أعم.")

    except GoogleAdsException as ex:
        print(
            f'❌  الطلب فشل بمعرف "{ex.request_id}" وحالة الخطأ هي '
            f'"{ex.error.code().name}"، وبتتضمن الأخطاء التالية:'
        )
        for error in ex.errors:
            print(f'\tرسالة الخطأ: "{error.message}".')
            if error.location:
                for field_path_element in error.location.field_path_elements:
                    print(f'\t\tفي الحقل: {field_path_element.field_name}')
    except Exception as e:
        print(f"❌  حصل خطأ غير متوقع: {e}")

if __name__ == "__main__":
    main()


from google.ads.googleads.client import GoogleAdsClient
from google.ads.googleads.errors import GoogleAdsException
import os # عشان نتحقق من وجود ملف الكونفيج

def main():
    # حمّل بيانات الاتصال من ملف google-ads.yaml
    # تأكد إن ملف google-ads.yaml موجود في نفس مكان السكريبت أو المسار بتاعه صحيح
    config_path = "google-ads.yaml"
    if not os.path.exists(config_path):
        print(f"⚠️  ملف الكونفيج {config_path} مش موجود. لازم تحطه في نفس مكان السكريبت.")
        print("تقدر تلاقي نموذج لملف الكونفيج في توثيقات Google Ads API.")
        return

    try:
        client = GoogleAdsClient.load_from_storage(config_path)
    except Exception as e:
        print(f"❌  فيه مشكلة في تحميل ملف الكونفيج {config_path}: {e}")
        print("تأكد إن الفورمات بتاع الملف صح وإن كل البيانات موجودة.")
        return

    # اختار الـ customer ID بتاعك (ممكن يكون 10 أرقام، لو مش معاك هديك طريقة تجيبه)
    # ده بيكون الـ Account ID بتاع حسابك في Google Ads بدون فواصل
    customer_id = "138-688-4705" # ده مجرد مثال، لازم تغيره بالـ ID بتاعك

    # اطلب من المستخدم يدخل موضوع الـ essay
    seed_keyword = input("اكتب موضوع الـ essay الأساسي بتاعك (مثلاً: Climate Change Impact): ")
    if not seed_keyword:
        print("مفيش موضوع دخلته، مش هقدر أجيب كلمات مفتاحية.")
        return

    # إعدادات البحث والخدمة
    keyword_plan_idea_service = client.get_service("KeywordPlanIdeaService")

    request = client.get_type("GenerateKeywordIdeasRequest")
    request.customer_id = customer_id
    
    # إضافة الكلمة الأساسية اللي دخلها المستخدم
    request.keyword_seed.keywords.append(seed_keyword)
    
    # تحديد الموقع الجغرافي: هنا بنحدد مصر (geoTargetConstants/1002711)
    request.geo_target_constants.append("geoTargetConstants/1002711")
    
    # تحديد اللغة: الإنجليزية (languageConstants/1003)
    request.language = "languageConstants/1003"
    
    # تحديد شبكة البحث: Google Search فقط
    # تم تصحيح هذا السطر: استخدام Google Search بدلاً من 'Google Search' اللي فيها مسافة
    request.keyword_plan_network = client.enums.KeywordPlanNetworkEnum.Google Search 

    print(f"\nجاري البحث عن كلمات مفتاحية مرتبطة بـ: '{seed_keyword}'...")

    # ابعت الطلب وحاول تتعامل مع أي أخطاء ممكن تحصل
    try:
        response = keyword_plan_idea_service.generate_keyword_ideas(request=request)

        print(f"\n🔍 أفضل كلمات مفتاحية ممكن تساعدك في الـ essay بتاعتك عن: {seed_keyword}\n")
        print(f"{'الكلمة المقترحة':<40} | {'متوسط البحث الشهري':<25} | {'المنافسة (للمعلنين)'}")
        print("-" * 100)

        found_ideas = False
        for idea in response:
            found_ideas = True
            keyword = idea.text
            
            # بنتحقق إذا كان الحقل موجود قبل ما نحاول نوصل للبيانات
            volume = idea.keyword_idea_metrics.avg_monthly_searches if idea.keyword_idea_metrics.HasField("avg_monthly_searches") else "غير متاح"
            competition = idea.keyword_idea_metrics.competition.name if idea.keyword_idea_metrics.HasField("competition") else "غير متاح"

            print(f"{keyword:<40} | {volume:<25} | {competition}")
        
        if not found_ideas:
            print("مفيش كلمات مفتاحية مقترحة حالياً للموضوع ده. ممكن تجرب موضوع تاني أو كلمة أساسية أعم.")

    except GoogleAdsException as ex:
        print(
            f'❌  الطلب فشل بمعرف "{ex.request_id}" وحالة الخطأ هي '
            f'"{ex.error.code().name}"، وبتتضمن الأخطاء التالية:'
        )
        for error in ex.errors:
            print(f'\tرسالة الخطأ: "{error.message}".')
            if error.location:
                for field_path_element in error.location.field_path_elements:
                    print(f'\t\tفي الحقل: {field_path_element.field_name}')
    except Exception as e:
        print(f"❌  حصل خطأ غير متوقع: {e}")

if __name__ == "__main__":
    main()


from google.ads.googleads.client import GoogleAdsClient
from google.ads.googleads.errors import GoogleAdsException
import os # عشان نتحقق من وجود ملف الكونفيج

def main():
    # حمّل بيانات الاتصال من ملف google-ads.yaml
    # تأكد إن ملف google-ads.yaml موجود في نفس مكان السكريبت أو المسار بتاعه صحيح
    config_path = "google-ads.yaml"
    if not os.path.exists(config_path):
        print(f"⚠️  ملف الكونفيج {config_path} مش موجود. لازم تحطه في نفس مكان السكريبت.")
        print("تقدر تلاقي نموذج لملف الكونفيج في توثيقات Google Ads API.")
        return

    try:
        client = GoogleAdsClient.load_from_storage(config_path)
    except Exception as e:
        print(f"❌  فيه مشكلة في تحميل ملف الكونفيج {config_path}: {e}")
        print("تأكد إن الفورمات بتاع الملف صح وإن كل البيانات موجودة.")
        return

    # اختار الـ customer ID بتاعك (ممكن يكون 10 أرقام، لو مش معاك هديك طريقة تجيبه)
    # ده بيكون الـ Account ID بتاع حسابك في Google Ads بدون فواصل
    customer_id = "138-688-4705" # ده مجرد مثال، لازم تغيره بالـ ID بتاعك

    # اطلب من المستخدم يدخل موضوع الـ essay
    seed_keyword = input("اكتب موضوع الـ essay الأساسي بتاعك (مثلاً: Climate Change Impact): ")
    if not seed_keyword:
        print("مفيش موضوع دخلته، مش هقدر أجيب كلمات مفتاحية.")
        return

    # إعدادات البحث والخدمة
    keyword_plan_idea_service = client.get_service("KeywordPlanIdeaService")

    request = client.get_type("GenerateKeywordIdeasRequest")
    request.customer_id = customer_id
    
    # إضافة الكلمة الأساسية اللي دخلها المستخدم
    request.keyword_seed.keywords.append(seed_keyword)
    
    # تحديد الموقع الجغرافي: هنا بنحدد مصر (geoTargetConstants/1002711)
    request.geo_target_constants.append("geoTargetConstants/1002711")
    
    # تحديد اللغة: الإنجليزية (languageConstants/1003)
    request.language = "languageConstants/1003"
    
    # تحديد شبكة البحث: Google Search فقط
    # تم تصحيح هذا السطر: استخدام GOOGLE_SEARCH بحروف كبيرة وشرطة سفلية
    request.keyword_plan_network = client.enums.KeywordPlanNetworkEnum.GOOGLE_SEARCH 

    print(f"\nجاري البحث عن كلمات مفتاحية مرتبطة بـ: '{seed_keyword}'...")

    # ابعت الطلب وحاول تتعامل مع أي أخطاء ممكن تحصل
    try:
        response = keyword_plan_idea_service.generate_keyword_ideas(request=request)

        print(f"\n🔍 أفضل كلمات مفتاحية ممكن تساعدك في الـ essay بتاعتك عن: {seed_keyword}\n")
        print(f"{'الكلمة المقترحة':<40} | {'متوسط البحث الشهري':<25} | {'المنافسة (للمعلنين)'}")
        print("-" * 100)

        found_ideas = False
        for idea in response:
            found_ideas = True
            keyword = idea.text
            
            # بنتحقق إذا كان الحقل موجود قبل ما نحاول نوصل للبيانات
            volume = idea.keyword_idea_metrics.avg_monthly_searches if idea.keyword_idea_metrics.HasField("avg_monthly_searches") else "غير متاح"
            competition = idea.keyword_idea_metrics.competition.name if idea.keyword_idea_metrics.HasField("competition") else "غير متاح"

            print(f"{keyword:<40} | {volume:<25} | {competition}")
        
        if not found_ideas:
            print("مفيش كلمات مفتاحية مقترحة حالياً للموضوع ده. ممكن تجرب موضوع تاني أو كلمة أساسية أعم.")

    except GoogleAdsException as ex:
        print(
            f'❌  الطلب فشل بمعرف "{ex.request_id}" وحالة الخطأ هي '
            f'"{ex.error.code().name}"، وبتتضمن الأخطاء التالية:'
        )
        for error in ex.errors:
            print(f'\tرسالة الخطأ: "{error.message}".')
            if error.location:
                for field_path_element in error.location.field_path_elements:
                    print(f'\t\tفي الحقل: {field_path_element.field_name}')
    except Exception as e:
        print(f"❌  حصل خطأ غير متوقع: {e}")

if __name__ == "__main__":
    main()


